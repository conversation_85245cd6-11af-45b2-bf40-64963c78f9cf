import { Request, Response } from "express";
import { STATUS_CODES } from "../../constants/statusCodes";
import { LIVE_STREAM_MESSAGES } from "../../constants/responseMessage";
import {
  createLiveStreamChannel,
  stopLiveStreamChannel,
  updateLiveStreamStatus,
} from "../../models/stream/stream.model";
import {
  calculateVideoLengthFromM3U8,
  ErrorResponse,
  SuccessResponse,
} from "../../utils/helper";
import { streamIO } from "../../sockets/stream";
import { LiveStreamService } from "../../services";
import { live2Service } from "../../services/streamService/live2.service";
import logger from "../../config/logger";
/**
 * Following function is responsible to initiate live stream
 * @param req
 * @param res
 */
export const httpUserLiveStreaming = async (req: Request, res: Response) => {
  const userId = req.user?._id;
  try {
    const result = await createLiveStreamChannel(req.body, userId);

    if (!result) {
      return ErrorResponse(
        res,
        STATUS_CODES.BAD_REQUEST,
        false,
        LIVE_STREAM_MESSAGES.INVALID_INPUT,
        []
      );
    }

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      LIVE_STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
      []
    );
  }
};

/**
 * Following controller is responsible to cancel live stream which just got initiated.
 * @param req
 * @param res
 */
export const httpCancelLivesStream = async (req: Request, res: Response) => {
  const streamKey = req.body.streamKey;
  try {
    const result = await stopLiveStreamChannel(streamKey);

    return SuccessResponse(
      res,
      result.statusCode,
      result.success,
      result.message,
      result.data
    );
  } catch (error) {
    return ErrorResponse(
      res,
      STATUS_CODES.INTERNAL_SERVER_ERROR,
      false,
      LIVE_STREAM_MESSAGES.INTERNAL_SERVER_ERROR
    );
  }
};

/**
 * Following function updates the stream status to live and isLive to true the moment user starts streaming.
 * @param req We accept streamkey = <userId>_<liveStreamId>
 * @param res Success response for starting the stream.
 * @returns result object of the function.
 */
export const handleStreamStart = async (req: Request, res: Response) => {
  const { name: streamKey } = req.body;

  try {
    const updatedStream = await updateLiveStreamStatus(streamKey, "live", true);
    if (!updatedStream) {
      return res
        .status(STATUS_CODES.NOT_FOUND)
        .json({ success: false, message: LIVE_STREAM_MESSAGES.NOT_FOUND });
    }
    streamIO.emit("streamStarted", { streamKey, updatedStream });
    res
      .status(STATUS_CODES.OK)
      .json({ success: true, message: LIVE_STREAM_MESSAGES.STARTED });
  } catch (error) {
    console.log("Error starting stream:", error);
    res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
      sucess: false,
      message: LIVE_STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
    });
  }
};

/**
 * Following function updates the stream status to "ended" and isLive = false. As soon as user stops the stream
 * @param req we accept streamKey = <userId>_<liveStreamId>
 * @param res we provide the success response for ending the stream.
 * @returns response
 */
export const handleStreamEnd = async (req: Request, res: Response) => {
  const { name: streamKey } = req.body;
  try {
    const videoLength = await calculateVideoLengthFromM3U8(streamKey);
    const updatedStream = await updateLiveStreamStatus(
      streamKey,
      "ended",
      false
    );

    if (!updatedStream) {
      return res
        .status(STATUS_CODES.NOT_FOUND)
        .json({ success: false, message: LIVE_STREAM_MESSAGES.NOT_FOUND });
    }
    res
      .status(STATUS_CODES.OK)
      .json({ success: true, message: LIVE_STREAM_MESSAGES.ENDED });
  } catch (error) {
    console.error("Error ending stream:", error);
    res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
      sucess: false,
      message: LIVE_STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
    });
  }
};

/**
 * Handle LLHLS (OvenMediaEngine) AdmissionWebhook requests
 *
 * This endpoint replaces the separate /live/start and /live/end endpoints for LLHLS streaming.
 * OvenMediaEngine sends webhook requests to this single endpoint for both stream lifecycle events.
 *
 * Expected webhook payload format:
 * {
 *   "request": {
 *     "application": "app",
 *     "stream": {
 *       "name": "userId_streamId"
 *     },
 *     "status": "opening" | "closing"
 *   }
 * }
 *
 * Authentication: Uses LLHLS_WEBHOOK_SECRET environment variable
 *
 * @param req Express request with webhook payload
 * @param res Express response
 */
export const handleLLHLSWebhook = async (req: Request, res: Response) => {
  let streamName = "unknown";

  try {
    const { request } = req.body;
    streamName = request.stream.name;
    const status = request.status; // "opening" or "closing"
    const application = request.application; // typically "app"

    logger.info("LLHLS webhook received:", {
      streamName,
      status,
      application,
      fullRequest: request
    });

    if (status === "opening") {
      // Stream is starting - equivalent to handleStreamStart
      const updatedStream = await updateLiveStreamStatus(streamName, "live", true);

      if (!updatedStream) {
        logger.warn("Stream not found for LLHLS start:", { streamName });
        return res
          .status(STATUS_CODES.NOT_FOUND)
          .json({ success: false, message: LIVE_STREAM_MESSAGES.NOT_FOUND });
      }

      // Emit socket event for real-time updates
      streamIO.emit("streamStarted", { streamKey: streamName, updatedStream });

      logger.info("LLHLS stream started successfully:", { streamName });
      return res
        .status(STATUS_CODES.OK)
        .json({ success: true, message: LIVE_STREAM_MESSAGES.STARTED });

    } else if (status === "closing") {
      // Stream is ending - equivalent to handleStreamEnd
      const videoLength = await calculateVideoLengthFromM3U8(streamName);
      const updatedStream = await updateLiveStreamStatus(
        streamName,
        "ended",
        false
      );

      if (!updatedStream) {
        logger.warn("Stream not found for LLHLS end:", { streamName });
        return res
          .status(STATUS_CODES.NOT_FOUND)
          .json({ success: false, message: LIVE_STREAM_MESSAGES.NOT_FOUND });
      }

      logger.info("LLHLS stream ended successfully:", { streamName });
      return res
        .status(STATUS_CODES.OK)
        .json({ success: true, message: LIVE_STREAM_MESSAGES.ENDED });

    } else {
      logger.warn("Unknown LLHLS webhook status:", { status, streamName });
      return res
        .status(STATUS_CODES.BAD_REQUEST)
        .json({ success: false, message: "Unknown webhook status" });
    }

  } catch (error) {
    logger.error("Error handling LLHLS webhook:", { error, streamName });
    return res.status(STATUS_CODES.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: LIVE_STREAM_MESSAGES.INTERNAL_SERVER_ERROR,
    });
  }
};

export const recordComplete = async (req: Request, res: Response) => {
  console.log("On-Play");
  console.log(req.body);
  res.status(200).json({ success: true });
};
export const recordReady = async (req: Request, res: Response) => {
  console.log("on_play-done");

  res.status(200).json({ success: true });
};

/**
 * Controller function to fetch Current live streams
 * @param req
 * @param res
 * @returns
 */
export const httpfetchLiveStreams = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const result = await LiveStreamService.getCurrentLiveStreams(page, limit);

    if (result.success) {
      return SuccessResponse(
        res,
        result.statusCode,
        result.success,
        result.message,
        result.data
      );
    } else {
      return ErrorResponse(
        res,
        result.statusCode,
        result.success,
        result.message,
        null
      );
    }
  } catch (error) {
    console.error("Error in getLiveStreams:", error);
    return ErrorResponse(res, 500, false, "Internal Server Error", null);
  }
};

export const isStreamLive = async (req: Request, res: Response) => {
  try {
    const streamId = req.params.streamId;
    if (!streamId) {
      return ErrorResponse(res, 400, false, "StreamId required");
    }
    
    // Check with live2 service first for real-time status
    const isLiveFromLive2 = await live2Service.isStreamLive(streamId);
    
    if (isLiveFromLive2) {
      return SuccessResponse(res, 200, true, "Stream is live", {
        isLive: true,
        streamKey: streamId,
        // Provide both HLS and LLHLS URLs for client flexibility
        urls: {
          hls: live2Service.getMasterPlaylistUrl(streamId),
          llhls: live2Service.getLLHLSMasterPlaylistUrl(streamId)
        }
      });
    }
    
    // Fallback to database check
    const stream = await LiveStreamService.checkStreamIsLive(streamId);

    return SuccessResponse(
      res,
      stream.statusCode,
      stream.success,
      stream.message,
      stream.data
    );
  } catch (error) {
    return ErrorResponse(res, 500, false, "Internal sever error", null);
  }
};

/**
 * Get streaming statistics from live2 container
 */
export const getStreamingStats = async (req: Request, res: Response) => {
  try {
    const stats = await live2Service.getStreamingStats();
    
    if (!stats) {
      return ErrorResponse(res, 503, false, "Streaming server unavailable", null);
    }

    return SuccessResponse(res, 200, true, "Stats retrieved successfully", stats);
  } catch (error) {
    return ErrorResponse(res, 500, false, "Failed to get streaming stats", null);
  }
};

/**
 * Get HLS and LLHLS URLs for a stream with different quality options
 */
export const getStreamUrls = async (req: Request, res: Response) => {
  try {
    const { streamKey } = req.params;

    if (!streamKey) {
      return ErrorResponse(res, 400, false, "Stream key required", null);
    }

    const urls = {
      // Legacy HLS URLs
      hls: {
        master: live2Service.getMasterPlaylistUrl(streamKey),
        qualities: live2Service.getAvailableQualities().map(quality => ({
          quality,
          url: live2Service.getHLSUrl(streamKey, quality)
        }))
      },
      // LLHLS URLs (Low Latency HLS)
      llhls: {
        master: live2Service.getLLHLSMasterPlaylistUrl(streamKey),
        qualities: live2Service.getAvailableQualities().map(quality => ({
          quality,
          url: live2Service.getLLHLSUrl(streamKey, quality)
        }))
      },
      // RTMP publish URL
      rtmp: live2Service.generateRTMPUrl(streamKey)
    };

    return SuccessResponse(res, 200, true, "Stream URLs generated", urls);
  } catch (error) {
    return ErrorResponse(res, 500, false, "Failed to generate stream URLs", null);
  }
};

/**
 * Health check for the streaming infrastructure
 */
export const healthCheck = async (req: Request, res: Response) => {
  try {
    const isHealthy = await live2Service.healthCheck();

    if (isHealthy) {
      return SuccessResponse(res, 200, true, "Streaming server is healthy", {
        status: "healthy",
        timestamp: new Date().toISOString()
      });
    } else {
      return ErrorResponse(res, 503, false, "Streaming server is unhealthy", {
        status: "unhealthy",
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    return ErrorResponse(res, 500, false, "Health check failed", null);
  }
};

/**
 * Get latency metrics for a specific stream
 */
export const getStreamLatencyMetrics = async (req: Request, res: Response) => {
  try {
    const { streamKey } = req.params;

    if (!streamKey) {
      return ErrorResponse(res, 400, false, "Stream key is required", null);
    }

    const metrics = await live2Service.getStreamLatencyMetrics(streamKey);

    if (!metrics) {
      return ErrorResponse(res, 404, false, "Stream not found or not active", null);
    }

    return SuccessResponse(res, 200, true, "Latency metrics retrieved", metrics);
  } catch (error) {
    logger.error("Error getting stream latency metrics:", error);
    return ErrorResponse(res, 500, false, "Failed to get latency metrics", null);
  }
};

/**
 * Get latency metrics for all active streams
 */
export const getAllLatencyMetrics = async (req: Request, res: Response) => {
  try {
    const metrics = await live2Service.getAllLatencyMetrics();

    return SuccessResponse(res, 200, true, "All latency metrics retrieved", {
      metrics,
      count: metrics.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error("Error getting all latency metrics:", error);
    return ErrorResponse(res, 500, false, "Failed to get latency metrics", null);
  }
};

/**
 * Get latency history for a stream
 */
export const getLatencyHistory = async (req: Request, res: Response) => {
  try {
    const { streamKey } = req.params;
    const windowSize = parseInt(req.query.windowSize as string) || 50;

    if (!streamKey) {
      return ErrorResponse(res, 400, false, "Stream key is required", null);
    }

    const history = live2Service.getLatencyHistory(streamKey);
    const recentHistory = history.slice(-windowSize);
    const averageLatency = live2Service.getAverageLatency(streamKey, Math.min(windowSize, 10));

    return SuccessResponse(res, 200, true, "Latency history retrieved", {
      streamKey,
      history: recentHistory,
      averageLatency,
      totalSamples: history.length,
      windowSize: recentHistory.length
    });
  } catch (error) {
    logger.error("Error getting latency history:", error);
    return ErrorResponse(res, 500, false, "Failed to get latency history", null);
  }
};

/**
 * Start latency monitoring
 */
export const startLatencyMonitoring = async (req: Request, res: Response) => {
  try {
    const intervalMs = parseInt(req.body.intervalMs) || 5000;

    if (intervalMs < 1000 || intervalMs > 60000) {
      return ErrorResponse(res, 400, false, "Interval must be between 1000ms and 60000ms", null);
    }

    live2Service.startLatencyMonitoring(intervalMs);

    return SuccessResponse(res, 200, true, "Latency monitoring started", {
      intervalMs,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error("Error starting latency monitoring:", error);
    return ErrorResponse(res, 500, false, "Failed to start latency monitoring", null);
  }
};

/**
 * Stop latency monitoring
 */
export const stopLatencyMonitoring = async (req: Request, res: Response) => {
  try {
    live2Service.stopLatencyMonitoring();

    return SuccessResponse(res, 200, true, "Latency monitoring stopped", {
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error("Error stopping latency monitoring:", error);
    return ErrorResponse(res, 500, false, "Failed to stop latency monitoring", null);
  }
};
