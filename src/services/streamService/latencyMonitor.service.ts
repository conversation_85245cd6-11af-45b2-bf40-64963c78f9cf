import axios from 'axios';
import logger from '../../config/logger';
import { EventEmitter } from 'events';

export interface LatencyMetrics {
  streamKey: string;
  timestamp: number;
  rtmpIngestLatency: number;
  segmentGenerationLatency: number;
  playlistUpdateLatency: number;
  endToEndLatency: number;
  chunkDuration: number;
  segmentDuration: number;
  bufferHealth: number;
  isHealthy: boolean;
}

export interface LatencyThresholds {
  rtmpIngestWarning: number;
  rtmpIngestCritical: number;
  endToEndWarning: number;
  endToEndCritical: number;
  segmentGenerationWarning: number;
  segmentGenerationCritical: number;
}

export class LatencyMonitorService extends EventEmitter {
  private monitoringInterval: NodeJS.Timeout | null = null;
  private readonly baseUrl: string;
  private readonly thresholds: LatencyThresholds;
  private readonly metricsHistory: Map<string, LatencyMetrics[]> = new Map();
  private readonly maxHistorySize = 100;

  constructor() {
    super();
    this.baseUrl = `http://localhost:8080`;
    this.thresholds = {
      rtmpIngestWarning: 500, // 500ms
      rtmpIngestCritical: 1000, // 1s
      endToEndWarning: 2000, // 2s
      endToEndCritical: 5000, // 5s
      segmentGenerationWarning: 200, // 200ms
      segmentGenerationCritical: 500, // 500ms
    };
  }

  /**
   * Start continuous latency monitoring
   */
  startMonitoring(intervalMs: number = 5000): void {
    if (this.monitoringInterval) {
      this.stopMonitoring();
    }

    logger.info('Starting latency monitoring', { intervalMs });
    
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.measureAllStreams();
      } catch (error) {
        logger.error('Error during latency monitoring:', error);
      }
    }, intervalMs);
  }

  /**
   * Stop latency monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      logger.info('Latency monitoring stopped');
    }
  }

  /**
   * Measure latency for all active streams
   */
  async measureAllStreams(): Promise<LatencyMetrics[]> {
    try {
      const activeStreams = await this.getActiveStreams();
      const metrics: LatencyMetrics[] = [];

      for (const streamKey of activeStreams) {
        try {
          const streamMetrics = await this.measureStreamLatency(streamKey);
          if (streamMetrics) {
            metrics.push(streamMetrics);
            this.storeMetrics(streamMetrics);
            this.checkThresholds(streamMetrics);
          }
        } catch (error) {
          logger.error(`Failed to measure latency for stream ${streamKey}:`, error);
        }
      }

      return metrics;
    } catch (error) {
      logger.error('Failed to measure latency for all streams:', error);
      return [];
    }
  }

  /**
   * Measure latency for a specific stream
   */
  async measureStreamLatency(streamKey: string): Promise<LatencyMetrics | null> {
    try {
      const startTime = Date.now();
      
      // Measure RTMP ingest latency by checking stream stats
      const rtmpLatencyStart = Date.now();
      const streamStats = await this.getStreamStats(streamKey);
      const rtmpIngestLatency = Date.now() - rtmpLatencyStart;

      if (!streamStats) {
        return null;
      }

      // Measure segment generation latency
      const segmentLatencyStart = Date.now();
      const playlistInfo = await this.getPlaylistInfo(streamKey);
      const segmentGenerationLatency = Date.now() - segmentLatencyStart;

      // Measure playlist update latency
      const playlistLatencyStart = Date.now();
      const playlistResponse = await this.fetchPlaylist(streamKey);
      const playlistUpdateLatency = Date.now() - playlistLatencyStart;

      // Calculate end-to-end latency
      const endToEndLatency = Date.now() - startTime;

      // Calculate buffer health (based on segment availability)
      const bufferHealth = this.calculateBufferHealth(playlistInfo);

      const metrics: LatencyMetrics = {
        streamKey,
        timestamp: Date.now(),
        rtmpIngestLatency,
        segmentGenerationLatency,
        playlistUpdateLatency,
        endToEndLatency,
        chunkDuration: playlistInfo?.chunkDuration || 0.1,
        segmentDuration: playlistInfo?.segmentDuration || 1,
        bufferHealth,
        isHealthy: this.isStreamHealthy(endToEndLatency, bufferHealth),
      };

      return metrics;
    } catch (error) {
      logger.error(`Failed to measure latency for stream ${streamKey}:`, error);
      return null;
    }
  }

  /**
   * Get active streams from OvenMediaEngine
   */
  private async getActiveStreams(): Promise<string[]> {
    try {
      const response = await axios.get(`${this.baseUrl}/stat`, {
        timeout: 3000,
        headers: { 'Accept': 'application/xml' }
      });

      // Parse XML to extract stream names
      const streamMatches = response.data.match(/<stream>[\s\S]*?<\/stream>/g) || [];
      const activeStreams: string[] = [];

      for (const streamXml of streamMatches) {
        const nameMatch = streamXml.match(/<name>(.*?)<\/name>/);
        const isPublishing = streamXml.includes('<publishing>') || streamXml.includes('<active>');
        
        if (nameMatch && isPublishing) {
          activeStreams.push(nameMatch[1]);
        }
      }

      return activeStreams;
    } catch (error) {
      logger.error('Failed to get active streams:', error);
      return [];
    }
  }

  /**
   * Get stream statistics
   */
  private async getStreamStats(streamKey: string): Promise<any> {
    try {
      const response = await axios.get(`${this.baseUrl}/stat`, {
        timeout: 2000,
        headers: { 'Accept': 'application/xml' }
      });

      // Parse stream-specific stats from XML
      const streamRegex = new RegExp(`<stream>.*?<name>${streamKey}</name>.*?</stream>`, 's');
      const streamMatch = response.data.match(streamRegex);
      
      if (streamMatch) {
        return { exists: true, data: streamMatch[0] };
      }
      
      return null;
    } catch (error) {
      logger.error(`Failed to get stats for stream ${streamKey}:`, error);
      return null;
    }
  }

  /**
   * Get playlist information
   */
  private async getPlaylistInfo(streamKey: string): Promise<any> {
    try {
      const response = await axios.get(`${this.baseUrl}/app/${streamKey}/llhls.m3u8`, {
        timeout: 2000,
        headers: { 'Accept': 'application/vnd.apple.mpegurl' }
      });

      // Parse M3U8 playlist to extract timing information
      const playlist = response.data;
      const segmentDurationMatch = playlist.match(/#EXT-X-TARGETDURATION:(\d+)/);
      const chunkDurationMatch = playlist.match(/#EXT-X-PART-INF:PART-TARGET=([\d.]+)/);
      
      return {
        segmentDuration: segmentDurationMatch ? parseFloat(segmentDurationMatch[1]) : 1,
        chunkDuration: chunkDurationMatch ? parseFloat(chunkDurationMatch[1]) : 0.1,
        segmentCount: (playlist.match(/#EXTINF:/g) || []).length,
        lastModified: response.headers['last-modified'],
      };
    } catch (error) {
      logger.debug(`Failed to get playlist info for stream ${streamKey}:`, error);
      return null;
    }
  }

  /**
   * Fetch playlist for latency measurement
   */
  private async fetchPlaylist(streamKey: string): Promise<any> {
    try {
      const response = await axios.get(`${this.baseUrl}/app/${streamKey}/llhls.m3u8`, {
        timeout: 1000,
        headers: { 'Accept': 'application/vnd.apple.mpegurl' }
      });
      
      return {
        status: response.status,
        size: response.data.length,
        lastModified: response.headers['last-modified'],
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Calculate buffer health score (0-100)
   */
  private calculateBufferHealth(playlistInfo: any): number {
    if (!playlistInfo) return 0;
    
    const { segmentCount, segmentDuration } = playlistInfo;
    const totalBufferTime = segmentCount * segmentDuration;
    
    // Ideal buffer time is 6-10 seconds for LLHLS
    const idealBufferTime = 8;
    const healthScore = Math.min(100, (totalBufferTime / idealBufferTime) * 100);
    
    return Math.max(0, healthScore);
  }

  /**
   * Check if stream is healthy based on latency and buffer
   */
  private isStreamHealthy(endToEndLatency: number, bufferHealth: number): boolean {
    return endToEndLatency < this.thresholds.endToEndWarning && bufferHealth > 50;
  }

  /**
   * Store metrics in history
   */
  private storeMetrics(metrics: LatencyMetrics): void {
    const { streamKey } = metrics;
    
    if (!this.metricsHistory.has(streamKey)) {
      this.metricsHistory.set(streamKey, []);
    }
    
    const history = this.metricsHistory.get(streamKey)!;
    history.push(metrics);
    
    // Keep only recent metrics
    if (history.length > this.maxHistorySize) {
      history.splice(0, history.length - this.maxHistorySize);
    }
  }

  /**
   * Check thresholds and emit alerts
   */
  private checkThresholds(metrics: LatencyMetrics): void {
    const { streamKey, endToEndLatency, rtmpIngestLatency, segmentGenerationLatency } = metrics;
    
    // Check end-to-end latency
    if (endToEndLatency > this.thresholds.endToEndCritical) {
      this.emit('latencyAlert', {
        level: 'critical',
        type: 'endToEnd',
        streamKey,
        value: endToEndLatency,
        threshold: this.thresholds.endToEndCritical,
        message: `Critical end-to-end latency: ${endToEndLatency}ms`
      });
    } else if (endToEndLatency > this.thresholds.endToEndWarning) {
      this.emit('latencyAlert', {
        level: 'warning',
        type: 'endToEnd',
        streamKey,
        value: endToEndLatency,
        threshold: this.thresholds.endToEndWarning,
        message: `High end-to-end latency: ${endToEndLatency}ms`
      });
    }
    
    // Check RTMP ingest latency
    if (rtmpIngestLatency > this.thresholds.rtmpIngestCritical) {
      this.emit('latencyAlert', {
        level: 'critical',
        type: 'rtmpIngest',
        streamKey,
        value: rtmpIngestLatency,
        threshold: this.thresholds.rtmpIngestCritical,
        message: `Critical RTMP ingest latency: ${rtmpIngestLatency}ms`
      });
    }
  }

  /**
   * Get metrics history for a stream
   */
  getMetricsHistory(streamKey: string): LatencyMetrics[] {
    return this.metricsHistory.get(streamKey) || [];
  }

  /**
   * Get average latency for a stream
   */
  getAverageLatency(streamKey: string, windowSize: number = 10): Partial<LatencyMetrics> | null {
    const history = this.getMetricsHistory(streamKey);
    if (history.length === 0) return null;
    
    const recentMetrics = history.slice(-windowSize);
    const avg = {
      rtmpIngestLatency: recentMetrics.reduce((sum, m) => sum + m.rtmpIngestLatency, 0) / recentMetrics.length,
      segmentGenerationLatency: recentMetrics.reduce((sum, m) => sum + m.segmentGenerationLatency, 0) / recentMetrics.length,
      playlistUpdateLatency: recentMetrics.reduce((sum, m) => sum + m.playlistUpdateLatency, 0) / recentMetrics.length,
      endToEndLatency: recentMetrics.reduce((sum, m) => sum + m.endToEndLatency, 0) / recentMetrics.length,
      bufferHealth: recentMetrics.reduce((sum, m) => sum + m.bufferHealth, 0) / recentMetrics.length,
    };
    
    return avg;
  }

  /**
   * Get current latency thresholds
   */
  getThresholds(): LatencyThresholds {
    return { ...this.thresholds };
  }

  /**
   * Update latency thresholds
   */
  updateThresholds(newThresholds: Partial<LatencyThresholds>): void {
    Object.assign(this.thresholds, newThresholds);
    logger.info('Latency thresholds updated:', this.thresholds);
  }
}

// Export singleton instance
export const latencyMonitorService = new LatencyMonitorService();
